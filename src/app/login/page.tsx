'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Lock, Chrome, Sparkles, Sun, Moon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { componentClasses, cn, toggleTheme, getCurrentTheme } from '@/utils/theme';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const router = useRouter();

  // Initialize theme
  useState(() => {
    setTheme(getCurrentTheme());
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setMessage('');

    try {
      const endpoint = isSignUp ? '/api/auth/signup' : '/api/auth/login';
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Authentication failed');
        return;
      }

      if (isSignUp && data.message) {
        setMessage(data.message);
      } else {
        // Redirect to main page on successful login
        router.push('/');
      }
    } catch (error) {
      console.error('Auth error:', error);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Google sign in failed');
        return;
      }

      if (data.url) {
        window.location.href = data.url;
      }
    } catch (error) {
      console.error('Google sign in error:', error);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Background gradients matching the main interface
  const lightCloudBg = 'linear-gradient(135deg, #F8FAFC 0%, #EDE9FE 25%, #DDD6FE 50%, #C4B5FD 75%, #A78BFA 100%)';
  const darkCloudBg = 'linear-gradient(135deg, #0F172A 0%, #1E293B 25%, #312E81 50%, #4C1D95 75%, #5B21B6 100%)';

  const lightCloudOverlay = `
    radial-gradient(ellipse 800px 600px at 20% 10%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse 600px 400px at 80% 30%, rgba(167, 139, 250, 0.12) 0%, transparent 50%),
    radial-gradient(ellipse 400px 300px at 40% 70%, rgba(196, 181, 253, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse 500px 350px at 70% 80%, rgba(221, 214, 254, 0.08) 0%, transparent 50%)
  `;

  const darkCloudOverlay = `
    radial-gradient(ellipse 800px 600px at 20% 10%, rgba(167, 139, 250, 0.2) 0%, transparent 50%),
    radial-gradient(ellipse 600px 400px at 80% 30%, rgba(196, 181, 253, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse 400px 300px at 40% 70%, rgba(221, 214, 254, 0.12) 0%, transparent 50%),
    radial-gradient(ellipse 500px 350px at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
  `;

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Cloud-like Background */}
      <div
        className="absolute inset-0"
        style={{
          background: theme === 'dark' ? darkCloudBg : lightCloudBg
        }}
      ></div>
      <div
        className="absolute inset-0 animate-cloud-drift"
        style={{
          background: theme === 'dark' ? darkCloudOverlay : lightCloudOverlay
        }}
      ></div>

      {/* Theme Toggle */}
      <motion.button
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        onClick={() => {
          const newTheme = toggleTheme(theme);
          setTheme(newTheme);
        }}
        className={cn('absolute top-8 right-8', componentClasses.button.secondary, 'hover:bg-secondary z-20')}
        title="Toggle theme"
      >
        {theme === 'dark' ? <Sun size={20} /> : <Moon size={20} />}
      </motion.button>

      {/* Login Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="relative z-10 w-full max-w-md mx-auto px-6"
      >
        <div className="bg-surface/95 backdrop-blur-xl border border-border-primary rounded-2xl shadow-large p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="w-16 h-16 mx-auto bg-gradient-primary rounded-2xl flex items-center justify-center shadow-brand mb-4"
            >
              <Sparkles className="text-white" size={28} />
            </motion.div>
            
            <motion.h1
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-2xl font-bold font-space-grotesk mb-2"
              style={{ color: 'var(--text-primary)' }}
            >
              Welcome to{' '}
              <span className="theme-gradient-text">zScore agent</span>
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-sm"
              style={{ color: 'var(--text-secondary)' }}
            >
              {isSignUp ? 'Create your account to get started' : 'Sign in to access your dashboard'}
            </motion.p>
          </div>

          {/* Error/Success Messages */}
          {(error || message) && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                "p-3 rounded-lg text-sm",
                error ? "bg-red-500/10 border border-red-500/20 text-red-600" : "bg-green-500/10 border border-green-500/20 text-green-600"
              )}
            >
              {error || message}
            </motion.div>
          )}

          {/* Form */}
          <motion.form
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            onSubmit={handleSubmit}
            className="space-y-6"
          >
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                Email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" size={18} />
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-surface border border-border-primary rounded-xl focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50 transition-all duration-200"
                  style={{ color: 'var(--text-primary)' }}
                  placeholder="Enter your email"
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" size={18} />
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-surface border border-border-primary rounded-xl focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50 transition-all duration-200"
                  style={{ color: 'var(--text-primary)' }}
                  placeholder="Enter your password"
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 px-4 bg-gradient-primary text-white rounded-xl font-medium hover:shadow-brand-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {isLoading ? 'Please wait...' : (isSignUp ? 'Create Account' : 'Sign In')}
            </button>
          </motion.form>

          {/* Divider */}
          <div className="my-6 flex items-center">
            <div className="flex-1 border-t border-border-primary"></div>
            <span className="px-4 text-sm text-text-tertiary">or</span>
            <div className="flex-1 border-t border-border-primary"></div>
          </div>

          {/* Google Sign In */}
          <motion.button
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className="w-full py-3 px-4 border border-border-primary rounded-xl font-medium hover:bg-surface-elevated disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-3"
            style={{ color: 'var(--text-primary)' }}
          >
            <Chrome size={20} />
            Continue with Google
          </motion.button>

          {/* Toggle Sign Up/Sign In */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            className="mt-6 text-center"
          >
            <div className="flex items-center justify-center gap-2 text-sm">
              <span style={{ color: 'var(--text-secondary)' }}>
                {isSignUp ? 'Already have an account?' : "Don't have an account?"}
              </span>
              <button
                type="button"
                onClick={() => {
                  setIsSignUp(!isSignUp);
                  setError('');
                  setMessage('');
                }}
                className="font-medium text-primary-500 hover:text-primary-600 transition-colors duration-200 underline"
              >
                {isSignUp ? 'Sign in' : 'Sign up'}
              </button>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
